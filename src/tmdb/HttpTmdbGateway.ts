import http from "http";
import CacheableLookup from "cacheable-lookup";
import got from "got";
import { HttpProxyAgent } from "hpagent";
import shuffle from "lodash.shuffle";
import { Readable } from "stream";

import { runConcurrently } from "../../lib/concurrent-worker.js";
import {
  DownloadAsManyImagesAsPossibleInput,
  TmdbGateway,
  TmdbGatewayFetchMovieResponse,
  TmdbGatewayFetchPersonResponse,
} from "../jobs/PullTmdbContentJob.js";
import JobPoolResource from "./JobPoolResource.js";
import ProxyPoolResource from "./ProxyPoolResource.js";

const proxies = `
178.253.236.139:8080
117.160.250.133:8080
103.210.57.243:80
3.89.91.40:80
173.212.200.30:3128
65.108.230.239:34079
103.82.227.22:3128
159.203.61.169:8080
162.223.94.164:80
117.160.250.137:8828
188.166.56.246:80
170.244.27.142:8888
123.163.52.24:9091
46.101.126.180:38245
58.57.170.154:9002
112.65.16.250:9002
120.236.74.210:9002
106.14.47.96:9401
220.248.70.237:9002
103.150.18.218:80
117.160.250.132:81
209.97.150.167:8080
103.155.54.26:83
49.0.252.39:9002
120.236.79.139:9002
118.107.44.181:8000
93.180.222.134:9991
117.160.250.163:8081
170.244.25.52:8888
117.160.250.132:8081
183.234.218.194:9002
223.210.2.228:9091
106.14.47.96:10051
85.235.184.186:3129
109.254.81.159:9090
117.160.250.130:82
196.6.235.26:8080
120.195.150.91:9091
103.138.31.206:8080
120.224.145.187:9091
124.90.209.63:8085
8.219.97.248:80
117.160.250.130:9999
36.77.91.82:80
183.233.169.226:9091
51.75.206.209:80
117.160.250.163:8828
82.210.8.173:80
134.209.29.120:3128
183.237.47.54:9091
181.238.40.29:8080
183.138.40.120:8085
172.104.41.13:16379
117.160.250.137:8080
117.160.250.133:81
34.111.224.139:8080
103.156.249.82:1111
121.22.53.166:9091
135.181.78.242:8118
45.234.61.2:999
221.231.13.198:1080
112.51.96.118:9091
202.110.67.141:9091
121.204.148.136:9002
60.175.244.38:9002
113.50.66.19:9091
111.225.152.24:8089
206.161.97.4:31337
117.160.250.134:8080
124.70.38.229:8080
111.225.153.4:8089
94.30.152.172:80
82.79.213.118:9812
128.199.202.122:8080
120.236.41.185:9091
202.131.159.206:1111
210.22.131.162:9002
111.8.226.107:9091
39.165.0.137:9002
120.194.4.157:5443
41.65.227.119:1976
115.74.246.138:8080
87.245.170.247:3128
106.14.47.96:54321
41.65.236.56:1981
123.138.214.150:9002
183.239.38.216:9091
103.16.118.28:8080
187.217.54.84:80
195.222.91.29:8080
109.200.155.195:8080
111.40.116.212:9091
61.191.56.60:8085
42.228.61.245:9091
175.136.66.125:80
43.252.11.194:8080
217.219.28.114:3128
111.225.152.247:8089
116.107.248.45:3333
120.37.121.209:9091
190.113.42.162:999
60.6.237.112:9002
62.193.108.153:1976
51.15.242.202:8888
46.249.125.11:8080
95.67.79.254:8080
5.161.203.20:8080
111.225.152.243:8089
46.101.186.238:80
93.177.126.79:8088
202.8.74.10:8080
14.53.144.77:80
183.220.6.198:9091
103.82.157.105:8080
114.102.47.178:8089
114.113.116.67:9091
183.234.219.91:9002
117.160.250.133:80
106.14.47.96:10801
146.70.81.248:8080
43.243.172.18:83
103.59.44.55:8085
95.87.30.11:8080
117.160.250.138:8081
134.209.29.120:8080
120.196.188.21:9091
181.129.183.19:53281
103.167.68.86:8080
117.160.250.130:8899
183.234.218.205:9002
118.97.164.19:8080
183.165.226.150:8089
39.175.92.35:30001
123.159.127.74:8085
117.160.250.138:8080
117.159.37.40:9091
193.31.27.123:80
170.244.25.123:8888
213.226.11.149:41878
114.116.123.178:3128
31.46.33.59:53281
203.150.128.18:8080
120.27.18.161:8088
218.7.171.91:3128
75.89.101.63:80
39.104.161.37:8082
120.237.144.200:9091
196.27.106.112:8080
49.156.23.170:3125
37.187.25.85:80
121.36.72.207:87
117.160.250.132:8899
198.52.115.114:1994
58.234.116.197:8193
41.33.142.181:1981
112.54.47.55:9091
110.164.208.125:8888
41.220.238.137:83
39.108.230.16:3128
170.244.27.61:8888
117.102.87.66:8080
103.126.87.25:3127
223.207.109.234:8080
46.175.1.65:8080
36.89.229.97:59707
110.164.162.45:80
106.14.47.96:10926
131.196.114.141:1994
168.0.239.225:8787
190.113.41.165:999
121.36.72.207:8082
13.81.217.201:80
203.177.133.235:8080
188.132.222.22:8080
121.89.218.157:3128
95.56.254.139:3128
41.65.236.44:1981
138.2.51.152:8080
103.143.170.130:8080
123.182.59.251:8089
85.132.67.138:80
103.36.11.161:3125
200.76.42.198:999
103.139.25.121:8080
60.12.168.114:9002
103.82.233.2:53281
173.212.195.139:80
183.221.242.102:9443
176.88.168.101:8080
62.193.68.67:1981
47.106.76.21:80
42.116.10.196:443
112.245.48.74:9002
112.11.242.201:9091
121.1.41.162:111
157.100.13.204:999
117.160.250.163:9990
65.20.224.193:80
186.1.44.70:8080
106.14.47.96:80
45.174.248.23:999
111.56.60.66:53281
102.164.252.150:8080
43.227.129.65:83
118.212.152.82:9091
45.119.82.101:3333
51.158.63.12:3128
165.16.80.109:1981
106.14.47.96:12345
194.169.167.5:8080
103.155.62.158:8080
159.203.61.169:3128
115.144.102.39:10080
106.14.47.96:10053
45.184.155.6:999
45.174.87.18:999
120.236.74.217:9002
27.147.209.215:8080
58.27.59.249:80
103.199.139.186:83
221.6.215.202:9091
157.90.236.24:8080
117.160.250.137:9999
106.14.47.96:10102
203.142.71.54:8080
167.71.5.83:3128
103.101.82.106:32650
200.106.184.21:999
18.179.20.228:8181
111.21.183.58:9091
209.97.150.167:3128
123.255.201.110:80
117.54.120.229:8888
110.34.1.180:32650
117.160.250.130:8081
170.244.27.150:8888
59.48.218.218:9091
117.160.250.130:8828
111.225.152.66:8089
179.49.113.230:999
158.69.212.254:80
103.160.232.122:8080
183.221.242.107:8443
47.91.95.174:8081
47.88.29.108:7302
34.120.53.182:80
109.72.109.210:8080
141.147.9.254:80
47.99.96.187:80
218.252.244.104:80
171.35.170.125:8085
117.159.10.124:9002
103.167.171.55:1111
45.228.234.59:999
190.93.179.125:8080
2.58.217.1:8080
103.215.207.38:83
120.236.64.75:9002
120.236.74.213:9002
103.183.113.52:3128
196.1.95.117:80
113.143.37.82:9002
59.57.50.114:9091
119.3.209.222:1234
221.231.3.238:9002
123.130.115.217:9091
114.55.59.28:8080
101.6.64.207:4780
189.127.90.82:8080
45.225.95.172:999
198.199.86.11:3128
112.98.177.27:9091
112.44.126.88:9091
117.160.250.130:8080
43.136.86.48:8080
41.93.71.21:80
168.235.97.254:3128
103.100.234.195:8080
4.16.68.158:443
106.14.47.96:10563
122.165.157.39:8080
34.146.64.228:3128
47.113.220.29:8888
196.44.224.253:8080
37.235.20.148:8080
36.134.91.82:8888
193.3.20.13:80
106.13.14.246:80
192.141.196.129:8080
123.171.1.196:8089
27.115.36.154:9002
189.202.239.174:999
123.182.59.18:8089
13.95.173.197:80
91.208.184.21:3128
120.236.66.134:9002
95.216.194.46:1081
190.64.131.250:45647
201.131.202.174:999
120.197.219.82:9091
211.97.2.197:9002
208.180.202.147:80
112.16.127.69:9002
41.57.154.35:6060
43.255.113.232:83
202.164.152.229:8080
117.160.250.130:80
124.71.149.10:3128
183.138.28.189:8085
58.34.41.219:8060
131.100.48.105:999
65.109.84.104:80
59.63.211.21:9002
157.230.241.133:39479
187.102.216.162:999
123.57.1.78:111
166.111.131.110:10080
103.86.187.190:808
112.6.178.53:8085
170.246.85.9:50991
198.199.86.11:8080
182.61.201.201:80
181.65.129.98:999
117.160.250.133:8828
168.205.102.26:8080
103.163.231.116:8080
103.219.60.57:3127
103.82.10.198:8989
120.236.74.211:9002
208.180.202.147:8080
85.222.163.82:8080
114.102.46.198:8089
170.83.242.251:999
36.170.93.124:3128
120.25.159.66:8118
92.118.232.74:80
103.43.151.36:80
103.167.134.31:80
159.203.104.153:8200
157.120.61.94:3128
89.252.152.138:2019
43.255.113.232:8081
41.65.227.118:1976
210.75.50.242:9002
114.55.84.12:30001
103.178.43.6:8181
198.52.241.2:999
3.143.37.255:80
103.15.140.121:44759
111.40.11.204:9091
185.73.103.23:3128
47.116.131.64:80
59.44.203.138:9091
58.57.170.146:9002
123.171.1.122:8089
222.88.167.22:9002
111.225.152.188:8089
165.16.46.193:8080
105.112.191.250:3128
178.49.14.57:3128
185.200.37.100:8080
41.65.71.145:8080
103.166.151.42:4995
41.254.44.70:8080
117.160.132.37:9091
186.0.171.217:8080
118.186.17.243:39665
117.159.15.99:9091
125.66.100.112:9091
104.223.135.178:10000
170.244.25.194:8888
47.56.110.204:8989
41.65.236.43:1981
103.152.112.145:80
103.85.114.240:8080
120.194.4.155:5443
111.40.62.199:9091
106.14.47.96:10800
170.83.242.249:999
186.96.141.208:3128
103.83.232.122:80
47.107.61.215:8000
117.158.146.215:9091
58.18.223.212:9002
41.222.62.41:9898
120.82.174.128:9091
162.223.94.163:80
31.161.38.233:8090
123.182.58.143:8089
103.75.160.250:83
202.61.204.51:80
185.200.38.195:8080
5.202.104.22:3128
112.194.142.135:9091
218.13.24.130:9002
182.253.159.51:8899
121.31.35.98:9091
140.246.114.169:59394
223.19.151.121:80
189.164.253.242:10101
103.165.30.251:32650
188.132.221.169:8080
197.246.212.70:3030
69.94.136.71:8443
49.232.201.174:8080
103.55.33.59:8080
46.209.207.152:8080
141.95.127.15:3128
58.18.223.211:9002
80.179.140.189:80
45.62.164.233:8080
183.230.198.80:9091
106.14.47.96:10001
122.155.165.191:3128
39.170.42.90:9091
121.204.165.184:9002
103.167.69.238:8080
123.159.126.27:8085
157.100.13.203:999
145.255.30.241:8088
58.20.235.231:9002
211.97.2.196:9002
103.146.197.93:8181
106.14.47.96:9443
182.253.193.218:8080
41.33.66.228:1981
123.171.42.36:8089
118.212.73.155:8085
123.182.59.186:8089
105.112.130.186:8080
103.123.234.106:8080
102.38.17.121:8080
200.125.171.61:999
106.14.47.96:10469
158.58.187.27:3128
65.108.230.239:46448
43.255.113.232:8083
179.189.48.255:8080
111.40.124.221:9091
123.157.233.138:9091
194.53.158.57:53281
89.252.152.229:2019
132.226.9.101:80
183.172.146.243:4780
20.99.187.69:8443
212.46.230.102:6969
184.168.122.103:7890
222.190.208.7:8089
177.38.10.15:8080
150.242.108.6:32650
178.208.66.215:3128
121.183.80.210:80
111.59.4.88:9002
106.14.47.96:21001
95.217.195.146:9999
192.236.160.186:80
106.14.140.161:80
176.32.2.193:8080
42.63.10.170:9002
65.108.230.239:46411
187.84.254.178:53382
41.65.227.111:1981
218.28.98.229:9091
117.160.250.138:8899
103.217.173.210:53905
117.160.250.130:81
221.193.240.115:9091
93.180.222.134:8181
93.1.195.28:8080
117.160.250.134:81
41.65.55.2:1981
103.155.217.105:41407
61.175.214.2:9091
191.179.219.128:8080
8.213.129.20:3129
112.250.110.172:9091
202.150.132.53:8080
167.71.5.83:8080
221.154.62.181:80
61.7.146.7:8082
117.74.65.207:5002
157.230.241.133:34481
101.200.187.233:8082
194.31.33.36:8080
142.147.114.50:8080
43.255.113.232:81
154.239.9.83:8080
117.160.250.134:8081
218.158.60.22:80
41.65.251.85:1976
195.181.152.71:3128
117.160.250.138:82
103.161.214.254:80
138.118.104.50:999
71.255.153.117:80
116.234.209.15:9002
200.105.215.22:33630
188.173.14.99:40666
120.202.23.192:3128
117.74.65.207:10443
106.14.47.96:10048
111.224.10.151:8089
103.156.17.60:8888
108.166.203.110:1994
65.20.224.193:8080
195.138.73.54:44017
80.232.242.125:9002
143.44.191.108:8080
111.0.65.4:9091
132.226.251.74:8443
222.65.225.148:8085
101.200.187.233:7777
177.55.247.41:8080
183.221.242.103:9443
88.99.42.107:3128
83.170.219.86:8081
186.72.101.86:999
200.24.154.149:999
41.65.236.37:1981
103.178.42.14:8181
139.159.176.147:8090
103.1.50.38:3125
85.192.13.66:8080
91.224.168.22:8080
62.193.68.67:1976
186.5.8.193:1994
103.187.74.17:8080
12.88.29.66:9080
78.138.98.115:3128
121.37.163.104:9001
113.161.131.43:80
202.180.20.66:8080
183.238.32.234:9002
121.37.207.154:1900
111.225.153.223:8089
183.239.62.59:9091
188.132.221.4:8080
220.248.238.26:9091
154.85.58.149:80
20.69.79.158:8443
60.210.40.190:9091
167.172.172.234:33359
188.165.226.95:8118
124.90.208.246:8085
139.162.78.109:8080
197.211.38.94:8080
113.208.119.142:9002
43.255.113.232:82
112.54.41.177:9091
117.160.250.131:82
117.74.65.207:8060
65.108.230.239:43827
78.20.209.210:443
43.255.113.232:8084
110.138.243.175:8080
47.98.219.185:8999
117.74.65.207:9090
80.240.202.218:8080
174.138.24.67:8080
117.160.250.132:9999
65.108.230.239:33585
124.13.181.4:80
117.74.65.207:8092
222.124.219.202:8080
183.233.219.26:9002
187.1.57.206:20183
118.107.44.181:80
117.74.65.207:9443
112.123.102.10:9002
89.43.10.141:80
103.94.52.70:3128
117.74.65.207:17328
123.159.127.188:8085
167.71.199.211:36057
139.59.1.14:3128
190.30.227.13:8080
117.74.65.207:1337
111.225.152.110:8089
103.181.45.9:80
106.14.47.96:10939
171.35.165.117:8085
175.100.86.17:8080
180.183.14.247:8080
140.238.210.181:80
118.34.105.254:8080
111.40.62.176:9091
152.32.202.108:80
102.68.85.187:80
62.193.108.157:1976
120.24.33.141:8000
182.16.185.12:8080
117.74.65.207:7001
216.137.184.253:80
157.100.54.24:999
200.24.158.219:999
190.171.161.62:8080
117.74.65.207:1883
45.189.112.65:999
117.74.65.207:1081
117.160.250.163:8080
130.41.109.158:8080
118.31.164.20:8082
203.89.126.250:80
117.74.65.207:5443
117.160.250.132:8828
213.136.101.40:3128
27.115.37.94:9002
178.184.243.230:8081
117.74.65.207:3127
46.47.197.210:3128
117.160.250.137:82
49.48.92.19:8080
177.136.86.225:999
185.79.243.153:31382
111.59.4.89:9002
200.123.2.171:3128
106.14.255.124:80
138.68.60.8:3128
124.13.181.6:80
183.230.162.122:9091
222.139.221.185:9091
190.92.239.132:8080
200.187.70.223:3128
117.74.65.207:9992
189.197.187.238:999
103.156.56.2:80
213.59.156.125:3128
167.71.199.211:38731
111.20.217.178:9091
149.56.96.252:9300
123.182.59.37:8089
89.252.152.137:2019
14.161.26.100:8080
5.135.170.126:8080
91.234.195.124:80
124.71.142.223:3128
178.253.241.43:8080
103.49.202.252:80
8.213.128.90:8808
39.175.75.144:30001
208.109.191.161:80
65.108.230.239:41963
117.74.65.207:2095
182.71.146.148:8080
47.109.36.177:80
61.53.66.116:9091
117.74.65.207:8499
124.90.14.209:8085
138.201.111.171:8080
111.225.153.53:8089
82.137.244.151:8080
138.121.161.86:8190
117.74.65.207:993
66.152.169.73:1994
120.27.239.26:7890
103.214.238.42:8080
163.53.82.161:32650
103.47.175.161:83
144.91.111.4:3128
123.201.21.234:8080
117.160.250.133:9999
39.104.79.145:9992
103.159.46.121:83
43.255.113.232:8080
117.74.65.207:87
186.4.161.31:3128
120.197.160.2:9002
36.91.98.115:8181
123.60.175.183:8888
204.137.250.6:3129
39.104.79.145:3333
74.208.219.109:3128
173.213.96.177:8111
123.182.59.236:8089
171.244.10.75:1911
201.71.2.107:999
117.74.65.207:5560
117.160.250.134:8899
94.102.228.126:32650
92.116.212.201:8118
183.222.217.168:9091
117.160.250.133:8081
117.74.65.207:8029
117.74.65.207:5678
119.3.14.229:8080
167.71.199.211:42623
170.244.27.242:8888
159.69.108.219:8080
112.26.81.142:9091
8.219.43.134:9050
117.160.250.134:9999
123.182.59.91:8089
113.195.207.249:9091
104.43.230.151:3128
103.164.213.78:8088
120.224.180.41:9002
185.33.181.41:80
153.101.67.170:9002
187.84.242.53:8080
103.123.25.65:80
64.225.4.17:9993
117.74.65.207:4443
149.129.93.183:8899
175.24.175.145:8080
123.171.42.78:8089
123.171.1.19:8089
140.210.198.96:3128
117.74.65.207:5801
115.219.10.191:8085
178.251.111.21:8080
213.6.197.162:19000
103.76.12.42:80
87.128.212.44:80
138.204.95.166:8080
117.160.250.137:8081
112.17.173.55:9091
117.160.250.163:9999
111.225.153.244:8089
49.0.199.132:3128
223.94.85.131:9091
93.180.222.134:8080
200.35.49.57:42541
111.225.152.50:8089
117.160.250.134:82
183.215.172.2:9091
47.100.201.85:80
174.70.1.210:8080
64.56.150.102:3128
47.252.27.174:8888
117.149.0.14:9091
117.74.65.207:8090
117.74.65.207:58208
190.110.99.189:999
103.149.130.38:80
176.196.48.114:8080
120.202.128.112:9002
103.76.12.42:8181
201.218.42.202:1994
110.52.103.74:9091
156.200.116.71:1976
117.74.65.207:808
176.110.121.90:21776
80.91.125.238:8089
220.87.121.155:80
14.17.94.9:443
117.160.250.134:80
180.235.65.13:80
111.225.153.218:8089
218.65.6.150:3128
8.210.127.228:8888
106.14.47.96:10086
49.0.199.132:9091
120.197.40.219:9002
165.16.114.136:8080
65.108.230.239:34847
182.106.220.252:9091
116.230.95.160:9002
116.113.68.130:9091
117.160.250.132:8080
117.160.250.163:80
117.74.65.207:13
47.88.87.74:1080
41.170.12.92:37444
103.53.77.162:8080
212.89.188.115:21231
124.79.115.104:9002
182.55.48.187:80
43.255.113.232:8085
51.83.98.90:80
111.225.153.56:8089
117.160.250.131:9999
213.232.127.252:80
111.225.153.58:8089
190.136.50.67:3128
181.45.149.43:3128
112.53.167.29:9091
117.160.250.163:82
43.251.135.19:8080
193.41.88.58:53281
170.233.193.129:999
154.236.179.226:1976
81.12.40.250:8080
103.118.46.176:8080
116.206.56.142:8080
181.78.94.238:999
116.63.128.247:87
116.63.128.247:84
93.94.178.70:8080
121.42.233.132:81
178.32.101.200:80
94.102.224.146:32650
113.57.84.39:9091
5.135.136.60:9090
124.90.208.45:8085
139.59.1.14:8080
124.71.235.205:4780
74.205.128.200:80
43.255.113.232:8082
45.175.160.34:999
222.247.57.67:9002
191.102.90.118:999
47.95.254.71:80
101.68.16.71:8085
116.193.172.44:8080
43.138.138.164:8080
82.165.190.65:80
222.180.240.62:9091
171.34.53.2:9091
123.207.26.110:7777
202.21.109.134:1012
106.14.47.96:10398
218.89.51.167:9091
65.108.230.239:38210
140.206.62.198:9002
177.229.210.50:8080
201.77.108.48:999
183.237.222.52:9002
72.170.220.17:8080
1.32.59.217:47045
62.193.108.138:1981
183.237.222.51:9002
185.15.244.162:80
123.159.126.14:8085
120.37.177.50:9091
61.183.234.226:9091
119.7.135.19:9091
39.185.232.150:9091
196.251.0.198:8080
117.160.250.133:82
61.111.38.5:80
183.247.152.98:53281
222.179.155.90:9091
194.190.43.63:53281
123.183.160.83:9091
65.0.160.35:8080
183.234.218.203:9002
123.182.58.64:8089
123.56.173.64:80
183.238.163.8:9002
119.51.64.3:8085
117.160.250.138:8828
47.74.152.29:8888
194.44.189.250:8080
61.158.175.38:9002
138.68.60.8:8080
116.117.253.212:9002
47.109.53.253:8889
103.92.26.190:4002
168.119.119.107:80
164.92.200.113:80
194.124.36.106:8080
181.204.21.164:999
212.127.88.225:8080
221.6.139.190:9002
47.92.242.45:1234
36.158.107.38:8085
223.29.214.2:80
102.129.157.110:8080
42.248.122.147:1080
117.160.250.163:81
89.252.152.246:2019
8.134.140.97:4455
201.217.49.2:80
54.86.198.153:80
123.112.215.16:9000
94.45.223.222:8080
148.76.97.250:80
120.234.203.171:9002
183.221.221.149:9091
111.225.153.77:8089
103.109.57.250:8889
45.174.79.133:999
114.102.44.68:8089
103.155.217.105:41416
47.92.248.86:8080
111.59.10.36:9002
190.187.201.26:8080
47.243.177.210:8088
193.175.196.212:3128
202.46.155.2:3328
103.90.156.248:8080
120.234.135.251:9002
157.120.44.53:80
103.117.192.14:80
175.184.234.19:8080
13.75.216.118:3128
62.193.108.150:1976
103.241.182.97:80
41.65.227.118:1981
123.205.34.233:80
221.10.250.51:9091
190.109.16.145:999
117.160.250.137:80
119.13.103.211:999
106.14.47.96:11208
103.176.96.179:8080
93.105.40.62:41258
77.233.5.68:55443
179.43.9.129:8085
62.193.108.143:1981
210.22.77.94:9002
206.1.92.101:999
178.208.66.92:3128
181.198.122.114:1994
136.243.19.90:3128
188.132.222.38:8080
161.35.70.249:3128
190.14.232.163:999
117.160.250.138:81
139.162.78.109:3128
195.190.144.6:80
200.123.29.40:3128
103.77.60.14:80
31.146.216.246:8080
41.184.188.25:8080
118.31.2.38:8999
46.176.232.251:3128
41.93.71.12:80
193.15.14.198:80
124.13.181.7:80
117.139.124.182:9091
183.138.6.221:8085
119.51.138.205:8085
106.14.47.96:10802
111.225.153.166:8089
103.75.196.121:80
197.45.109.146:1981
103.111.122.2:80
61.29.96.146:8000
146.70.81.248:8081
99.56.147.242:56250
117.158.173.216:9091
111.225.152.151:8089
170.244.27.58:8888
201.91.82.155:3128
183.223.24.122:9091
36.34.159.90:9091
171.244.28.105:3128
190.237.238.14:999
182.253.246.249:8080
138.118.105.131:999
161.35.70.249:8080
134.209.108.182:8888
120.196.186.248:9091
124.90.209.207:8085
52.86.125.18:80
88.150.15.30:8080
121.128.194.154:80
106.14.47.96:10619
35.246.140.159:3128
58.57.170.150:9002
201.229.250.21:8080
120.202.23.193:3128
111.225.153.208:8089
123.171.1.204:8089
108.71.252.121:3128
84.22.49.26:8080
106.14.47.96:10568
185.15.172.212:3128
209.141.62.12:5555
163.177.106.4:8001
210.22.161.86:9002
46.0.203.186:8080
157.254.193.6:80
111.8.226.108:9091
43.255.113.232:86
179.61.229.162:999
111.225.152.167:8089
218.217.82.172:80
65.108.230.239:40471
43.255.113.232:8086
41.128.148.77:1976
61.133.66.69:9002
223.113.80.158:9091
103.176.96.41:8082
143.0.176.136:8088
112.6.174.110:9091
165.227.81.188:9997
89.252.152.208:2019
43.255.113.232:80
45.71.38.99:1994
116.230.95.0:9002
216.74.255.182:80
154.239.9.82:8080
117.160.250.133:8899
58.246.58.150:9002
20.241.236.196:3128
117.160.250.138:9999
103.120.175.243:9191
123.183.162.9:9002
89.162.196.78:8081
5.75.229.87:8080
176.118.49.98:53281
5.104.174.199:23500
181.37.240.89:999
24.230.33.96:3128
103.197.251.202:80
195.158.9.179:55555
87.255.12.183:3129
154.236.189.23:1981
210.17.255.58:8080
58.20.248.139:9002
111.47.170.136:9091
117.160.250.137:81
222.175.59.6:9002
117.160.250.134:8828
103.52.0.173:8080
14.170.154.193:19132
95.0.171.40:8080
117.160.250.131:8081
117.40.176.42:9091
223.84.240.36:9091
65.20.190.208:8080
117.160.250.132:82
170.244.26.36:8888
120.237.57.83:9091
211.138.6.37:9091
111.225.152.42:8089
72.44.76.76:1994
8.130.39.117:9999
81.92.157.14:8181
203.19.38.114:1080
198.11.175.192:8081
94.74.80.88:3128
223.215.176.106:8089
91.107.229.5:8080
193.138.247.36:3128
117.54.114.32:80
168.181.196.76:8080
222.190.208.90:8089
83.40.137.251:80
103.117.95.136:41890
183.165.249.56:8089
140.237.128.104:8089
167.235.150.96:8080
201.217.246.178:8080
103.106.219.50:8080
117.54.114.98:80
103.121.149.69:8080
191.102.64.146:999
103.145.113.78:80
59.59.163.17:8089
131.100.51.250:999
103.157.117.8:8080
103.169.42.250:8080
223.247.47.50:8089
200.24.147.66:999
103.171.83.174:3125
171.22.25.170:80
152.69.197.238:8080
123.245.248.16:8089
121.206.205.233:8089
191.52.251.60:3128
154.118.228.212:80
183.164.244.46:8089
120.34.231.87:8089
180.119.94.135:8089
161.35.214.127:35843
45.114.145.108:32650
188.82.97.82:80
38.158.92.205:8080
103.160.206.184:32650
220.179.211.139:8089
191.97.9.186:999
186.226.185.82:666
222.190.208.207:8089
45.112.127.77:8080
117.54.114.101:80
196.1.214.179:80
94.182.234.63:8080
117.69.230.5:8089
45.234.61.173:999
180.168.191.195:9002
186.52.187.196:3128
103.105.76.183:8080
154.0.155.205:8080
45.149.77.141:7000
163.53.82.168:32650
45.234.61.3:999
114.99.2.7:8089
45.191.130.121:45005
5.78.31.93:8443
114.231.45.10:8089
45.113.64.29:3129
177.136.86.146:999
45.225.185.62:999
103.168.44.114:8080
161.35.214.127:35543
222.190.173.43:8089
103.176.96.131:8080
117.54.114.96:80
103.168.247.1:8080
103.175.156.142:8080
110.34.3.229:3128
125.17.80.226:8080
120.34.253.243:8089
45.6.201.255:8080
85.236.170.146:80
100.42.79.61:1994
103.162.154.200:8888
103.155.217.105:41411
103.155.217.156:41482
120.40.213.199:8089
103.155.217.156:41456
117.69.230.203:8089
168.0.239.224:8787
27.157.248.118:8089
103.152.40.216:32650
121.230.210.240:8089
95.0.84.26:80
103.136.36.10:8080
114.237.209.2:8089
187.95.34.135:8080
103.25.167.130:3129
41.161.92.138:8080
102.68.129.54:8080
104.194.240.13:8080
223.215.177.247:8089
177.53.214.19:999
41.65.236.44:1976
45.114.145.178:32650
121.230.210.2:8089
165.225.210.219:10515
117.54.114.97:80
27.150.85.44:8089
183.165.224.242:8089
81.16.245.179:53281
49.0.2.242:8090
27.157.228.49:8089
123.245.250.35:8089
102.223.20.217:80
***************:8089
**************:8089
**************:8089
***************:8089
*************:8089
**************:8089
***************:8089
**************:8089
***************:8089
*************:8123
*************:8089
`
  .trim()
  .split("\n")
  .map((line) => `http://${line}`)
  .map(
    (proxy) =>
      new HttpProxyAgent({
        keepAlive: true,
        proxy,
      }),
  );

const dnsCache = new CacheableLookup({
  cache: new Map([
    [
      "api.themoviedb.org",
      [
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
      ],
    ],
    [
      "image.tmdb.org",
      [
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
        { address: "*************", family: 4 },
      ],
    ],
  ]),
});

const MAX_CONCURRENT_WORKERS = 30;

export default class HttpTmdbGateway implements TmdbGateway {
  private config?: Configuration;

  constructor(
    private apiKey: string,
    private debug: (message: string) => void,
  ) {}

  async fetchMovie(
    tmdbId: string,
  ): Promise<TmdbGatewayFetchMovieResponse | null> {
    const movie = await this.getMovie(tmdbId);

    if (!movie) {
      return null;
    }

    const configuration = await this.getConfiguration();

    return {
      genres: uniqBy(
        movie.genres.map((genre) => ({
          id: String(genre.id),
          name: genre.name,
        })),
        (genre) => genre.id,
      ),
      images: [...movie.images.backdrops]
        .sort(
          (a, b) =>
            (b.iso_639_1 === null ? 1 : 0) - (a.iso_639_1 === null ? 1 : 0) ||
            b.vote_average - a.vote_average,
        )
        .map((backdrop) => ({
          sizes: configuration.images.backdrop_sizes.map((size) => {
            const width =
              size === "original" ? backdrop.width : Number(size.slice(1));

            return {
              height: Math.round(width / backdrop.aspect_ratio),
              url: `${configuration.images.base_url}${size}${backdrop.file_path}`,
              width,
            };
          }),
          type: "backdrop" as const,
        })),
      keywords: movie.keywords.keywords.map((keyword) => ({
        id: String(keyword.id),
        name: keyword.name,
      })),
    };
  }

  async fetchPerson(
    tmdbId: string,
  ): Promise<TmdbGatewayFetchPersonResponse | null> {
    const person = await this.getPerson(tmdbId);

    if (!person) {
      return null;
    }

    const configuration = await this.getConfiguration();

    return {
      images: person.profile_path
        ? [
            {
              sizes: [
                {
                  height: 180,
                  width: 180,
                  url: `${configuration.images.base_url}w180_and_h180_face${person.profile_path}`,
                },
              ],
            },
          ]
        : [],
    };
  }

  async downloadAsManyImagesAsPossible(
    inputs: DownloadAsManyImagesAsPossibleInput[],
    callback: (
      stream: Readable,
      image: DownloadAsManyImagesAsPossibleInput,
    ) => Promise<void>,
  ): Promise<void> {
    const jobsPool = new JobPoolResource(inputs);
    const proxiesPool = new ProxyPoolResource(shuffle(proxies));

    const numberOfWorkers = Math.min(
      MAX_CONCURRENT_WORKERS,
      proxies.length,
      inputs.length,
    );

    await runConcurrently(
      async (proxy: http.Agent, job: DownloadAsManyImagesAsPossibleInput) => {
        const image = got.stream({
          method: "GET",
          headers: {},
          url: new URL(job.sourceUrl),
          dnsCache,
          agent: {
            http: proxy,
          },
          timeout: 5_000,
        });

        const timeout = setTimeout(() => {
          image.destroy(new Error("TIMEOUT"));
        }, 5_000);

        await callback(image, job).finally(() => clearTimeout(timeout));
      },
      [proxiesPool, jobsPool],
      { numberOfWorkers },
    );

    if (jobsPool.getFailedJobsCount() > 0) {
      this.debug(`Failed to download ${jobsPool.getFailedJobsCount()} images`);
    }
  }

  private async getMovie(tmdbId: string): Promise<Movie | null> {
    const url = new URL(`movie/${tmdbId}`, "https://api.themoviedb.org/3/");

    url.searchParams.set("api_key", this.apiKey);
    url.searchParams.set(
      "append_to_response",
      ["images", "keywords"].join(","),
    );

    const response = await got(url, {
      method: "GET",
      headers: {},
      dnsCache,
      throwHttpErrors: false,
      timeout: 5_000,
      retry: 1,
    }).catch((error) => {
      if (
        error instanceof Error &&
        (error.message === "incorrect header check" ||
          error.message.includes("Timeout awaiting 'request'"))
      ) {
        return;
      }

      throw error;
    });

    if (!response) {
      return null;
    }

    if (response.statusCode === 404) {
      return null;
    }

    if (response.statusCode !== 200) {
      const body = JSON.parse(response.body) as {
        status_message: string;
      };

      throw new Error(body.status_message);
    }

    return JSON.parse(response.body) as Movie;
  }

  private async getPerson(tmdbId: string): Promise<Person | null> {
    const url = new URL(`person/${tmdbId}`, "https://api.themoviedb.org/3/");

    url.searchParams.set("api_key", this.apiKey);
    url.searchParams.set("append_to_response", ["images"].join(","));

    const response = await got(url, {
      method: "GET",
      headers: {},
      dnsCache,
      throwHttpErrors: false,
      timeout: 5_000,
      retry: 1,
    }).catch((error) => {
      if (
        error instanceof Error &&
        (error.message === "incorrect header check" ||
          error.message.includes("Timeout awaiting 'request'"))
      ) {
        return;
      }

      throw error;
    });

    if (!response) {
      return null;
    }

    if (response.statusCode === 404) {
      return null;
    }

    if (response.statusCode !== 200) {
      const body = JSON.parse(response.body) as {
        status_message: string;
      };

      throw new Error(body.status_message);
    }

    return JSON.parse(response.body) as Person;
  }

  private async getConfiguration(): Promise<Configuration> {
    if (this.config) {
      return this.config;
    }

    const url = new URL("configuration", "https://api.themoviedb.org/3/");

    url.searchParams.set("api_key", this.apiKey);

    const response = await got(url, {
      method: "GET",
      headers: {},
      throwHttpErrors: false,
      dnsCache,
    });

    if (response.statusCode !== 200) {
      const body = JSON.parse(response.body) as {
        status_message: string;
      };

      throw new Error(body.status_message);
    }

    this.config = JSON.parse(response.body) as Configuration;

    return this.config;
  }
}

interface Configuration {
  images: {
    base_url: string;
    secure_base_url: string;
    backdrop_sizes: string[];
    logo_sizes: string[];
    poster_sizes: string[];
    profile_sizes: string[];
    still_sizes: string[];
  };
  change_keys: string[];
}

interface Movie {
  adult: boolean;
  backdrop_path: string;
  belongs_to_collection: null;
  budget: number;
  genres: {
    id: number;
    name: string;
  }[];
  homepage: string;
  id: number;
  imdb_id: string;
  original_language: string;
  original_title: string;
  overview: string;
  popularity: number;
  poster_path: string;
  production_companies: {
    id: number;
    logo_path: string | null;
    name: string;
    origin_country: string;
  }[];
  production_countries: {
    iso_3166_1: string;
    name: string;
  }[];
  release_date: string;
  revenue: number;
  runtime: number;
  spoken_languages: {
    english_name: string;
    iso_639_1: string;
    name: string;
  }[];
  status: "Released";
  tagline: string;
  title: string;
  video: boolean;
  vote_average: number;
  vote_count: number;
  images: {
    backdrops: {
      aspect_ratio: number;
      height: number;
      iso_639_1: null;
      file_path: string;
      vote_average: number;
      vote_count: number;
      width: number;
    }[];
    logos: {
      aspect_ratio: number;
      height: number;
      iso_639_1: null;
      file_path: string;
      vote_average: number;
      vote_count: number;
      width: number;
    }[];
    posters: {
      aspect_ratio: number;
      height: number;
      iso_639_1: null;
      file_path: string;
      vote_average: number;
      vote_count: number;
      width: number;
    }[];
  };
  keywords: {
    keywords: {
      id: number;
      name: string;
    }[];
  };
}

interface Person {
  adult: boolean;
  also_known_as: string[];
  biography: string | null;
  birthday: string | null;
  deathday: string | null;
  gender: 1 | 2;
  homepage: string | null;
  id: number;
  imdb_id: string | null;
  known_for_department: string | null;
  name: string | null;
  place_of_birth: string | null;
  popularity: number;
  profile_path: string | null;
}

function uniqBy<X>(xs: X[], key: (t: X) => string): X[] {
  const keys = new Set<string>();
  const result: X[] = [];

  xs.forEach((x) => {
    if (!keys.has(key(x))) {
      keys.add(key(x));
      result.push(x);
    }
  });

  return result;
}
